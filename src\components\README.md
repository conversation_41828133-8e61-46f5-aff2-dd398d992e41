# Reusable React Components

This directory contains modern, responsive React components built with TypeScript, Tailwind CSS, and Framer Motion.

## Components

### 1. Navbar Component

A fully responsive navigation bar with smooth animations and accessibility features.

#### Features:
- **Responsive Design**: Adapts to all screen sizes with hamburger menu on mobile
- **Smooth Animations**: Framer Motion powered micro-animations
- **Accessibility**: Full ARIA support and keyboard navigation
- **Active Link Highlighting**: Shows current page with visual indicators
- **Scroll Effects**: Changes appearance on scroll
- **Modern Design**: Gradient logo, soft shadows, and hover effects

#### Usage:
```tsx
import { Navbar } from './components';

function App() {
  return (
    <>
      <Navbar />
      {/* Your page content */}
    </>
  );
}
```

#### Customization:
- Edit `navItems` array in `Navbar.tsx` to modify navigation links
- Adjust colors by modifying Tailwind classes
- Change logo by updating the `Code2` icon and text

### 2. Footer Component

A comprehensive footer with multiple sections and social links.

#### Features:
- **Multi-Section Layout**: About, Quick Links, Projects, and Social sections
- **Responsive Grid**: Adapts layout based on screen size
- **Social Media Integration**: Configurable social links with hover effects
- **Back to Top Button**: Smooth scroll to top functionality
- **Contact Information**: Display contact details with icons
- **Consistent Styling**: Matches navbar design language

#### Usage:
```tsx
import { Footer } from './components';

function Page() {
  return (
    <div>
      {/* Your page content */}
      <Footer />
    </div>
  );
}
```

#### Customization:
- Edit `quickLinks`, `projectLinks`, and `socialLinks` arrays
- Update contact information in the About section
- Modify colors and styling through Tailwind classes

## Installation

Make sure you have the required dependencies:

```bash
npm install framer-motion lucide-react
```

## Dependencies

- **React**: ^18.3.1
- **React Router DOM**: ^6.30.0
- **Framer Motion**: Latest version for animations
- **Lucide React**: For icons
- **Tailwind CSS**: For styling

## Accessibility Features

Both components include:
- ARIA labels and attributes
- Keyboard navigation support
- Focus management
- Screen reader compatibility
- Semantic HTML structure

## Browser Support

- Modern browsers (Chrome, Firefox, Safari, Edge)
- Mobile browsers (iOS Safari, Chrome Mobile)
- Responsive design for all screen sizes

## Performance

- Lazy loading compatible
- Optimized animations
- Minimal bundle impact
- Tree-shakeable exports
