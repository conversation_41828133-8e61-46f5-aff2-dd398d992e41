import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, FileText, PenTool, <PERSON>, Zap } from 'lucide-react';

function StudyPlatformPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          <img
            src="https://res.cloudinary.com/dramsks0e/image/upload/v1750755613/AI-driven-educational-platform_ufzllw.png"
            alt="Study Platform"
            className="w-full h-96 object-cover"
          />
          
          <div className="p-8">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">AI-Powered Study Content Generator</h1>
            
            <div className="flex flex-wrap gap-2 mb-8">
              <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">React</span>
              <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">TypeScript</span>
              <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">AWS Amplify</span>
              <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">AI/ML</span>
            </div>

            <div className="prose max-w-none">
              <p className="text-lg text-gray-600 mb-8">
                An advanced educational platform that leverages AI to transform lecture materials into
                summarized content and interactive quizzes. The system processes various document formats,
                generates concise summaries, and creates assessment materials automatically.
              </p>

              <h2 className="text-2xl font-bold text-gray-900 mb-4">Key Features</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                <FeatureCard
                  icon={<Brain className="w-6 h-6" />}
                  title="AI Processing"
                  description="Intelligent document analysis and content generation"
                />
                <FeatureCard
                  icon={<FileText className="w-6 h-6" />}
                  title="Smart Summaries"
                  description="Automated generation of concise educational content"
                />
                <FeatureCard
                  icon={<PenTool className="w-6 h-6" />}
                  title="Quiz Generation"
                  description="AI-powered creation of assessment materials"
                />
                <FeatureCard
                  icon={<Users className="w-6 h-6" />}
                  title="User Management"
                  description="Role-based access for students and administrators"
                />
                <FeatureCard
                  icon={<BookOpen className="w-6 h-6" />}
                  title="Content Review"
                  description="Administrative workflow for content approval"
                />
                <FeatureCard
                  icon={<Zap className="w-6 h-6" />}
                  title="Real-time Updates"
                  description="Instant content delivery and progress tracking"
                />
              </div>

              <h2 className="text-2xl font-bold text-gray-900 mb-4">Technical Details</h2>
              <ul className="list-disc pl-6 text-gray-600 mb-8">
                <li>React and TypeScript frontend with Vite</li>
                <li>AWS Amplify Gen 2 backend infrastructure</li>
                <li>Google Gemini AI for content processing</li>
                <li>Serverless architecture with AWS Lambda</li>
                <li>DynamoDB for data storage</li>
                <li>S3 for document storage</li>
                <li>Cognito for authentication</li>
              </ul>

              <h2 className="text-2xl font-bold text-gray-900 mb-4">Results</h2>
              <ul className="list-disc pl-6 text-gray-600">
                <li>Reduced content creation time by 70%</li>
                <li>High accuracy in content summarization</li>
                <li>Positive feedback from educators and students</li>
                <li>Scalable infrastructure handling thousands of users</li>
              </ul>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}

interface FeatureCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
}

function FeatureCard({ icon, title, description }: FeatureCardProps) {
  return (
    <div className="bg-gray-50 p-6 rounded-lg">
      <div className="text-blue-600 mb-3">{icon}</div>
      <h3 className="text-lg font-semibold text-gray-900 mb-2">{title}</h3>
      <p className="text-gray-600">{description}</p>
    </div>
  );
}

export default StudyPlatformPage;