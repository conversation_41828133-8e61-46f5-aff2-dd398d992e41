import React from 'react';
import { Briefcase } from 'lucide-react';

interface JobExperience {
  title: string;
  company: string;
  period: string;
  responsibilities: string[];
}

const jobExperiences: JobExperience[] = [
  {
    title: 'Senior Software Engineer',
    company: 'TechCorp Solutions',
    period: '2021 - Present',
    responsibilities: [
      'Led development of cloud-native microservices architecture',
      'Reduced system latency by 40% through optimization',
      'Mentored junior developers and conducted code reviews'
    ]
  },
  {
    title: 'Full Stack Developer',
    company: 'InnovateTech',
    period: '2019 - 2021',
    responsibilities: [
      'Developed and maintained multiple client-facing applications',
      'Implemented CI/CD pipelines reducing deployment time by 60%',
      'Collaborated with UX team to improve user experience'
    ]
  }
];

const EmploymentSection: React.FC = () => {
  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 className="text-3xl font-bold text-gray-900 mb-12 text-center">Employment History</h2>
        <div className="space-y-12">
          {jobExperiences.map((job, index) => (
            <div key={index} className="relative pl-8 border-l-2 border-blue-500">
              <div className="absolute w-4 h-4 bg-blue-500 rounded-full -left-[9px] top-0"></div>
              <div className="bg-white p-6 rounded-lg shadow-md">
                <div className="flex items-center mb-2">
                  <Briefcase className="w-5 h-5 text-blue-600 mr-2" />
                  <h3 className="text-xl font-semibold text-gray-900">{job.title}</h3>
                </div>
                <p className="text-blue-600 font-medium mb-2">{job.company} • {job.period}</p>
                <ul className="list-disc list-inside text-gray-600 space-y-2">
                  {job.responsibilities.map((responsibility, respIndex) => (
                    <li key={respIndex}>{responsibility}</li>
                  ))}
                </ul>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default EmploymentSection;
