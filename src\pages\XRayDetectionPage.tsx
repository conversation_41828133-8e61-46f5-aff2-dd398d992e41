import React from 'react';
import { <PERSON>, Database, BarChart as Chart, Shield, Code2, Server } from 'lucide-react';

function XRayDetectionPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          <img
            src="https://res.cloudinary.com/dramsks0e/image/upload/v1750755613/Medical-AI-X-ray_bcln1g.png"
            alt="X-Ray Detection System"
            className="w-full h-96 object-cover"
          />
          
          <div className="p-8">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">Chest X-ray Pneumonia Detection</h1>
            
            <div className="flex flex-wrap gap-2 mb-8">
              <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">Python</span>
              <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">TensorFlow</span>
              <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">Keras</span>
              <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">Deep Learning</span>
            </div>

            <div className="prose max-w-none">
              <p className="text-lg text-gray-600 mb-8">
                An AI-powered diagnostic tool using deep learning to automatically detect pneumonia from chest X-ray images.
                The system achieves high accuracy and provides rapid screening capabilities to assist healthcare professionals
                in diagnosing pneumonia efficiently and accurately.
              </p>

              <h2 className="text-2xl font-bold text-gray-900 mb-4">Key Features</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                <FeatureCard
                  icon={<Brain className="w-6 h-6" />}
                  title="Deep Learning Model"
                  description="MobileNet-based CNN architecture optimized for medical image analysis"
                />
                <FeatureCard
                  icon={<Database className="w-6 h-6" />}
                  title="Dataset Handling"
                  description="Advanced data preprocessing and augmentation techniques"
                />
                <FeatureCard
                  icon={<Chart className="w-6 h-6" />}
                  title="Performance Metrics"
                  description="Comprehensive evaluation using precision, recall, and ROC curves"
                />
                <FeatureCard
                  icon={<Shield className="w-6 h-6" />}
                  title="Validation"
                  description="Rigorous testing and validation on diverse X-ray datasets"
                />
                <FeatureCard
                  icon={<Code2 className="w-6 h-6" />}
                  title="Clean Implementation"
                  description="Well-documented, modular code following best practices"
                />
                <FeatureCard
                  icon={<Server className="w-6 h-6" />}
                  title="Scalable Design"
                  description="Efficient processing pipeline for handling large datasets"
                />
              </div>

              <h2 className="text-2xl font-bold text-gray-900 mb-4">Technical Details</h2>
              <ul className="list-disc pl-6 text-gray-600 mb-8">
                <li>Implemented using TensorFlow 2.0 and Keras</li>
                <li>MobileNet architecture with transfer learning</li>
                <li>Data augmentation for improved model generalization</li>
                <li>Class balancing through oversampling</li>
                <li>Comprehensive evaluation metrics and visualization</li>
                <li>Modular codebase with clear documentation</li>
                <li>Efficient data processing pipeline</li>
              </ul>

              <h2 className="text-2xl font-bold text-gray-900 mb-4">Results</h2>
              <ul className="list-disc pl-6 text-gray-600">
                <li>High accuracy on test dataset</li>
                <li>Balanced precision and recall metrics</li>
                <li>Strong ROC curve performance</li>
                <li>Validated against multiple datasets</li>
              </ul>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}

interface FeatureCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
}

function FeatureCard({ icon, title, description }: FeatureCardProps) {
  return (
    <div className="bg-gray-50 p-6 rounded-lg">
      <div className="text-blue-600 mb-3">{icon}</div>
      <h3 className="text-lg font-semibold text-gray-900 mb-2">{title}</h3>
      <p className="text-gray-600">{description}</p>
    </div>
  );
}

export default XRayDetectionPage;