import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { visualizer } from 'rollup-plugin-visualizer';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react(),
    visualizer({
      filename: 'dist/stats.html',
      open: false,
      gzipSize: true,
      brotliSize: true,
    }),
  ],
  optimizeDeps: {
    include: ['react', 'react-dom', 'react-router-dom'],
    exclude: ['lucide-react'],
  },
  build: {
    target: 'es2015',
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
      },
    },
    rollupOptions: {
      output: {
        manualChunks: (id) => {
          // Vendor chunk for core React libraries
          if (id.includes('node_modules/react') || id.includes('node_modules/react-dom')) {
            return 'vendor-react';
          }
          // Router chunk
          if (id.includes('node_modules/react-router')) {
            return 'vendor-router';
          }
          // Icons chunk
          if (id.includes('node_modules/lucide-react')) {
            return 'vendor-icons';
          }
          // Other vendor libraries
          if (id.includes('node_modules')) {
            return 'vendor-misc';
          }
          // Page-specific chunks
          if (id.includes('src/pages/HomePage')) {
            return 'page-home';
          }
          if (id.includes('src/pages/XRayDetectionPage')) {
            return 'page-xray';
          }
          if (id.includes('src/pages/NetCommHubPage')) {
            return 'page-netcomm';
          }
          if (id.includes('src/pages/StudyPlatformPage')) {
            return 'page-study';
          }
          if (id.includes('src/pages/TextSummarizationPage')) {
            return 'page-text';
          }
        },
      },
    },
    chunkSizeWarningLimit: 1000,
  },
  server: {
    hmr: {
      overlay: false,
    },
  },
});
