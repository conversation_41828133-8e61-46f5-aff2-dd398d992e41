<svg viewBox="0 0 800 600" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="800" height="600" fill="#f0f4f8"/>
  
  <!-- Title -->
  <text x="400" y="40" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#1a365d">
    AI-Powered Pneumonia Detection System
  </text>
  
  <!-- Input Section - Chest X-rays -->
  <g transform="translate(50, 80)">
    <!-- Normal X-ray -->
    <rect x="0" y="0" width="120" height="140" fill="#ffffff" stroke="#4a5568" stroke-width="2" rx="8"/>
    <rect x="10" y="10" width="100" height="100" fill="#e2e8f0" rx="4"/>
    <!-- Lung shapes for normal -->
    <ellipse cx="35" cy="60" rx="20" ry="35" fill="#cbd5e0" opacity="0.7"/>
    <ellipse cx="75" cy="60" rx="20" ry="35" fill="#cbd5e0" opacity="0.7"/>
    <text x="60" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#2d3748">Normal</text>
    
    <!-- Pneumonia X-ray -->
    <rect x="140" y="0" width="120" height="140" fill="#ffffff" stroke="#4a5568" stroke-width="2" rx="8"/>
    <rect x="150" y="10" width="100" height="100" fill="#e2e8f0" rx="4"/>
    <!-- Lung shapes for pneumonia with opacities -->
    <ellipse cx="175" cy="60" rx="20" ry="35" fill="#cbd5e0" opacity="0.7"/>
    <ellipse cx="215" cy="60" rx="20" ry="35" fill="#cbd5e0" opacity="0.7"/>
    <!-- Pneumonia indicators -->
    <circle cx="180" cy="55" r="8" fill="#e53e3e" opacity="0.6"/>
    <circle cx="210" cy="65" r="10" fill="#e53e3e" opacity="0.6"/>
    <circle cx="195" cy="75" r="7" fill="#e53e3e" opacity="0.6"/>
    <text x="200" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#2d3748">Pneumonia</text>
  </g>
  
  <!-- Arrow from X-rays to CNN -->
  <path d="M 315 150 L 375 150" stroke="#4a5568" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- CNN Architecture (MobileNet) -->
  <g transform="translate(380, 80)">
    <!-- Network layers visualization -->
    <rect x="0" y="0" width="40" height="120" fill="#4299e1" stroke="#2b6cb0" stroke-width="2" rx="4"/>
    <rect x="50" y="10" width="35" height="100" fill="#48bb78" stroke="#2f855a" stroke-width="2" rx="4"/>
    <rect x="95" y="20" width="30" height="80" fill="#ed8936" stroke="#c05621" stroke-width="2" rx="4"/>
    <rect x="135" y="30" width="25" height="60" fill="#9f7aea" stroke="#6b46c1" stroke-width="2" rx="4"/>
    <rect x="170" y="40" width="20" height="40" fill="#f56565" stroke="#c53030" stroke-width="2" rx="4"/>
    
    <!-- MobileNet label -->
    <text x="105" y="140" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#2d3748">MobileNet CNN</text>
    <text x="105" y="155" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#4a5568">(96% Accuracy)</text>
  </g>
  
  <!-- Arrow from CNN to Output -->
  <path d="M 575 140 L 625 140" stroke="#4a5568" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Output Classification -->
  <g transform="translate(630, 91)">
    <rect x="0" y="0" width="120" height="100" fill="#ffffff" stroke="#4a5568" stroke-width="2" rx="8"/>
    <text x="60" y="20" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#2d3748">Classification</text>
    <rect x="12" y="30" width="95" height="27" fill="#f56565" rx="4"/>
    <text x="60" y="47" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">Normal: 4%</text>
    <rect x="12" y="62" width="95" height="27" fill="#48bb78" rx="4"/>
    <text x="60" y="79" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">Pneumonia: 96%</text>
  </g>
  
  <!-- Data Augmentation Visualization -->
  <g transform="translate(50, 280)">
    <text x="0" y="0" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#1a365d">Data Augmentation Process</text>
    
    <!-- Original image -->
    <rect x="0" y="20" width="80" height="80" fill="#ffffff" stroke="#4a5568" stroke-width="2" rx="4"/>
    <rect x="10" y="30" width="60" height="60" fill="#e2e8f0" rx="2"/>
    <ellipse cx="40" cy="60" rx="15" ry="25" fill="#cbd5e0" opacity="0.7"/>
    <text x="40" y="115" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#2d3748">Original</text>
    
    <!-- Augmented versions -->
    <!-- Rotated -->
    <rect x="100" y="20" width="80" height="80" fill="#ffffff" stroke="#4a5568" stroke-width="2" rx="4"/>
    <rect x="110" y="30" width="60" height="60" fill="#e2e8f0" rx="2"/>
    <g transform="rotate(15, 140, 60)">
      <ellipse cx="140" cy="60" rx="15" ry="25" fill="#cbd5e0" opacity="0.7"/>
    </g>
    <text x="140" y="115" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#2d3748">Rotated</text>
    
    <!-- Zoomed -->
    <rect x="200" y="20" width="80" height="80" fill="#ffffff" stroke="#4a5568" stroke-width="2" rx="4"/>
    <rect x="210" y="30" width="60" height="60" fill="#e2e8f0" rx="2"/>
    <ellipse cx="240" cy="60" rx="20" ry="30" fill="#cbd5e0" opacity="0.7"/>
    <text x="240" y="115" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#2d3748">Zoomed</text>
    
    <!-- Flipped -->
    <rect x="300" y="20" width="80" height="80" fill="#ffffff" stroke="#4a5568" stroke-width="2" rx="4"/>
    <rect x="310" y="30" width="60" height="60" fill="#e2e8f0" rx="2"/>
    <g transform="scale(-1, 1) translate(-680, 0)">
      <ellipse cx="340" cy="60" rx="15" ry="25" fill="#cbd5e0" opacity="0.7"/>
    </g>
    <text x="340" y="115" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#2d3748">Flipped</text>
  </g>
  
  <!-- Dataset Balance Visualization -->
  <g transform="translate(455, 270)">
    <text x="0" y="5" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#1a365d">Dataset Balancing</text>
    
    <!-- Before -->
    <g transform="translate(0, 20)">
      <text x="0" y="15" font-family="Arial, sans-serif" font-size="12" fill="#4a5568">Before:</text>
      <rect x="0" y="25" width="40" height="60" fill="#48bb78" stroke="#2f855a" stroke-width="1"/>
      <rect x="0" y="85" width="120" height="60" fill="#f56565" stroke="#c53030" stroke-width="1"/>
      <text x="60" y="60" font-family="Arial, sans-serif" font-size="10" fill="#2d3748">1:3 ratio</text>
    </g>
    
    <!-- After -->
    <g transform="translate(180, 20)">
      <text x="0" y="15" font-family="Arial, sans-serif" font-size="12" fill="#4a5568">After:</text>
      <rect x="0" y="25" width="60" height="60" fill="#48bb78" stroke="#2f855a" stroke-width="1"/>
      <rect x="0" y="85" width="60" height="60" fill="#f56565" stroke="#c53030" stroke-width="1"/>
      <text x="80" y="60" font-family="Arial, sans-serif" font-size="10" fill="#2d3748">1:1 ratio</text>
    </g>
  </g>
  
  <!-- Key Metrics -->
  <g transform="translate(50, 450)">
    <rect x="0" y="0" width="700" height="100" fill="#ffffff" stroke="#cbd5e0" stroke-width="2" rx="8"/>
    <text x="350" y="25" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#1a365d">Performance Metrics</text>
    
    <!-- Metrics -->
    <g transform="translate(50, 50)">
      <circle cx="0" cy="0" r="4" fill="#48bb78"/>
      <text x="10" y="5" font-family="Arial, sans-serif" font-size="14" fill="#2d3748">Accuracy: 96.36%</text>
    </g>
    
    <g transform="translate(200, 50)">
      <circle cx="0" cy="0" r="4" fill="#4299e1"/>
      <text x="10" y="5" font-family="Arial, sans-serif" font-size="14" fill="#2d3748">Precision: 96.30%</text>
    </g>
    
    <g transform="translate(350, 50)">
      <circle cx="0" cy="0" r="4" fill="#9f7aea"/>
      <text x="10" y="5" font-family="Arial, sans-serif" font-size="14" fill="#2d3748">Recall: 99.05%</text>
    </g>
    
    <g transform="translate(480, 50)">
      <circle cx="0" cy="0" r="4" fill="#ed8936"/>
      <text x="10" y="5" font-family="Arial, sans-serif" font-size="14" fill="#2d3748">F1-Score: 97.65%</text>
    </g>
    
    <g transform="translate(250, 75)">
      <circle cx="0" cy="0" r="4" fill="#f56565"/>
      <text x="10" y="5" font-family="Arial, sans-serif" font-size="14" fill="#2d3748">5,873 Total Images | 63 Epochs</text>
    </g>
  </g>
  
  <!-- Arrow marker definition -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#4a5568"/>
    </marker>
  </defs>
</svg>