import React from 'react';
import { Award } from 'lucide-react';

interface Certificate {
  title: string;
  issuer: string;
  year: string;
}

const certificates: Certificate[] = [
  {
    title: 'AWS Certified Cloud Practitioner',
    issuer: 'Amazon Web Services',
    year: '2023'
  }
];

const CertificatesSection: React.FC = () => {
  return (
    <section className="py-16 bg-white">
      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 className="text-3xl font-bold text-gray-900 mb-12 text-center">Certificates</h2>
        <div className="space-y-6">
          {certificates.map((certificate, index) => (
            <div 
              key={index} 
              className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg shadow-lg p-6 flex items-center"
            >
              <div className="bg-white rounded-full p-3 mr-6">
                <Award className="w-8 h-8 text-blue-600" />
              </div>
              <div>
                <h3 className="text-xl font-semibold text-white mb-2">{certificate.title}</h3>
                <p className="text-blue-100">Issued by {certificate.issuer} • {certificate.year}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default CertificatesSection;
